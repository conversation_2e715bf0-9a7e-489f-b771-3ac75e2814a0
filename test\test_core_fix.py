#!/usr/bin/env python3
"""
测试核心修复逻辑
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_qt_orientation_fix():
    """测试Qt::Orientation修复"""
    print("🔧 测试Qt::Orientation修复...")
    
    try:
        from PyQt5.QtCore import Qt, QObject, pyqtSignal, QCoreApplication
        
        # 创建最小应用程序上下文
        if not QCoreApplication.instance():
            app = QCoreApplication([])
        
        # 测试Qt枚举类型
        orientation = Qt.Orientation.Horizontal
        sort_order = Qt.SortOrder.AscendingOrder
        print(f"✅ Qt枚举类型可用: {orientation}, {sort_order}")
        
        # 测试信号定义
        class TestHeader(QObject):
            sortIndicatorChanged = pyqtSignal(int, Qt.SortOrder)
            
            def __init__(self):
                super().__init__()
                self.signal_received = False
            
            def on_sort_changed(self, index, order):
                print(f"✅ 信号处理成功: index={index}, order={order}")
                self.signal_received = True
        
        # 测试DirectConnection
        header = TestHeader()
        header.sortIndicatorChanged.connect(
            header.on_sort_changed,
            Qt.DirectConnection
        )
        
        # 发射信号
        header.sortIndicatorChanged.emit(0, Qt.SortOrder.AscendingOrder)
        
        if header.signal_received:
            print("✅ DirectConnection信号连接成功")
            return True
        else:
            print("❌ DirectConnection信号连接失败")
            return False
            
    except Exception as e:
        print(f"❌ Qt::Orientation修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_sync_manager():
    """测试ConfigSyncManager单例修复"""
    print("🔧 测试ConfigSyncManager单例修复...")
    
    try:
        from src.modules.data_import.config_sync_manager import ConfigSyncManager
        
        # 测试单例模式
        instance1 = ConfigSyncManager.get_instance()
        instance2 = ConfigSyncManager.get_instance()
        
        if instance1 is instance2:
            print("✅ ConfigSyncManager单例模式正常")
            return True
        else:
            print("❌ ConfigSyncManager单例模式失败")
            return False
            
    except Exception as e:
        print(f"❌ ConfigSyncManager测试失败: {e}")
        return False

def test_sort_state_protection():
    """测试排序状态保护机制"""
    print("🔧 测试排序状态保护机制...")
    
    try:
        # 模拟排序状态保护逻辑
        class MockTable:
            def __init__(self):
                self._is_applying_sort = False
                self.sort_calls = 0
            
            def _save_and_apply_sort_state(self, sort_columns):
                # 模拟原代码的循环防护逻辑
                if hasattr(self, '_is_applying_sort') and self._is_applying_sort:
                    print("🔧 正在应用排序，跳过重复调用")
                    return
                
                # 设置排序应用标志
                self._is_applying_sort = True
                self.sort_calls += 1
                
                # 模拟排序操作
                print(f"🔧 执行排序操作: {sort_columns}")
                
                # 重置标志
                self._is_applying_sort = False
        
        # 测试防护机制
        table = MockTable()
        
        # 第一次调用应该成功
        table._save_and_apply_sort_state(['column1'])
        
        # 模拟并发调用
        table._is_applying_sort = True
        table._save_and_apply_sort_state(['column2'])  # 应该被跳过
        
        if table.sort_calls == 1:
            print("✅ 排序状态保护机制正常")
            return True
        else:
            print("❌ 排序状态保护机制失败")
            return False
            
    except Exception as e:
        print(f"❌ 排序状态保护测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("🔧 开始测试核心修复...")
    print()
    
    tests = [
        test_qt_orientation_fix,
        test_config_sync_manager,
        test_sort_state_protection
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            print()
    
    print(f"🎉 测试完成：{passed}/{total} 个测试通过")
    
    if passed == total:
        print("✅ 所有核心修复测试通过！")
        print("✅ 现在排序异常退出问题应该已经解决了。")
        print("✅ 主要修复包括：")
        print("  - Qt::Orientation类型使用DirectConnection")
        print("  - ConfigSyncManager单例模式防止资源冲突")
        print("  - 排序状态保护机制防止循环调用")
        return True
    else:
        print("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)