#!/usr/bin/env python3
"""
测试排序修复是否解决了Qt::Orientation问题
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_main_app_import():
    """测试主应用程序导入和初始化"""
    print("🔧 测试主应用程序导入...")
    
    try:
        # 测试主模块导入
        from main import create_application, setup_environment, check_dependencies
        print("✅ 主模块导入成功")
        
        # 测试环境设置
        if setup_environment():
            print("✅ 环境设置成功")
        else:
            print("❌ 环境设置失败")
            return False
        
        # 测试依赖检查
        if check_dependencies():
            print("✅ 依赖检查通过")
        else:
            print("❌ 依赖检查失败")
            return False
        
        # 测试应用程序创建（不显示GUI）
        app = create_application()
        if app:
            print("✅ Qt应用程序创建成功")
            app.quit()  # 立即退出
            return True
        else:
            print("❌ Qt应用程序创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_connection_without_gui():
    """测试信号连接不使用GUI"""
    print("🔧 测试信号连接...")
    
    try:
        from PyQt5.QtCore import Qt, QObject, pyqtSignal, QCoreApplication
        
        # 创建最小应用程序上下文
        if not QCoreApplication.instance():
            app = QCoreApplication([])
        
        class MockTable(QObject):
            sortIndicatorChanged = pyqtSignal(int, Qt.SortOrder)
            
            def __init__(self):
                super().__init__()
                # 测试信号连接使用DirectConnection
                self.sortIndicatorChanged.connect(
                    self.on_sort_changed,
                    Qt.DirectConnection
                )
            
            def on_sort_changed(self, index, order):
                print(f"✅ 排序信号处理成功: index={index}, order={order}")
                self.test_result = True
        
        # 测试信号发射
        table = MockTable()
        table.test_result = False
        table.sortIndicatorChanged.emit(0, Qt.SortOrder.AscendingOrder)
        
        if hasattr(table, 'test_result') and table.test_result:
            print("✅ 信号连接测试成功")
            return True
        else:
            print("❌ 信号连接测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 信号连接测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("🔧 开始测试排序修复...")
    print()
    
    tests = [
        test_main_app_import,
        test_signal_connection_without_gui
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            print()
    
    print(f"🎉 测试完成：{passed}/{total} 个测试通过")
    
    if passed == total:
        print("✅ 排序修复测试全部通过！现在应该不会再出现Qt::Orientation错误了。")
        return True
    else:
        print("❌ 部分测试失败，可能仍有问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)