#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多列排序管理器

实现表格的多列排序功能，支持：
- 多个字段同时排序（最多3个字段）
- 升序、降序、取消排序
- 排序指示器显示
- 排序状态管理
- 与序号列的兼容性

创建时间: 2025-07-01
作者: 月度工资异动处理系统开发团队
"""

import pandas as pd
from typing import List, Tuple, Dict, Any, Optional
from enum import Enum
from dataclasses import dataclass

from PyQt5.QtWidgets import QTableWidget, QHeaderView
from PyQt5.QtCore import Qt, QObject, pyqtSignal
from PyQt5.QtGui import QIcon, QPainter, QColor, QPen

# 导入项目内部模块
try:
    from src.utils.log_config import setup_logger
except ImportError:
    # 如果无法导入，使用标准日志
    import logging
    def setup_logger(name):
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger


class SortOrder(Enum):
    """排序顺序枚举"""
    NONE = "none"           # 无排序
    ASCENDING = "ascending"  # 升序
    DESCENDING = "descending"  # 降序


@dataclass
class SortColumn:
    """排序列信息"""
    column_index: int
    order: SortOrder
    priority: int  # 排序优先级，数值越小优先级越高
    column_name: str = ""


class MultiColumnSortManager(QObject):
    """
    多列排序管理器
    
    管理表格的多列排序功能，提供：
    - 多个字段同时排序
    - 排序优先级管理
    - 排序状态指示
    - 与现有表格组件的集成
    """
    
    # 信号定义
    sort_changed = pyqtSignal(list)  # 排序发生变化时发出，参数为排序列表
    sort_applied = pyqtSignal(object)  # 排序应用完成，参数为排序后的数据
    
    def __init__(self, table_widget: QTableWidget, max_sort_columns: int = 3):
        """
        初始化多列排序管理器
        
        Args:
            table_widget: 关联的表格组件
            max_sort_columns: 最大排序列数
        """
        super().__init__()
        
        self.logger = setup_logger(__name__)
        self.table_widget = table_widget
        self.max_sort_columns = max_sort_columns
        
        # 排序状态
        self.sort_columns: List[SortColumn] = []
        self.original_data = None
        self.current_sorted_data = None
        
        # 是否启用序号列（现在使用垂直表头，不占用数据列）
        self.has_row_number_column = False
        
        # 排序历史记录
        self.sort_history = []
        self.max_history_size = 10
        
        # 设置排序功能
        self.setup_sort_functionality()
        
        self.logger.info(f"多列排序管理器初始化完成，最大排序列数: {max_sort_columns}")
    
    def setup_sort_functionality(self):
        """设置排序功能"""
        try:
            # 启用表格排序
            self.table_widget.setSortingEnabled(True)
            
            # 获取表头
            header = self.table_widget.horizontalHeader()
            
            # 设置排序指示器
            header.setSortIndicatorShown(True)
            header.setHighlightSections(True)
            
            # 🔧 [信号连接修复] 确保槽函数正确绑定到实例
            # 使用lambda包装，确保方法正确绑定
            def sort_handler(logical_index, order):
                try:
                    self._on_sort_indicator_changed(logical_index, order)
                except Exception as handler_error:
                    self.logger.error(f"排序处理器执行失败: {handler_error}")
            
            # 🔧 [排序修复] 使用更安全的信号连接方式
            try:
                # 方法1: 使用lambda包装的处理器
                header.sortIndicatorChanged.connect(sort_handler)
                self.logger.debug("✅ 排序信号连接成功 (lambda包装)")
            except Exception as conn_error:
                try:
                    # 方法2: 尝试使用Qt.QueuedConnection
                    self.logger.warning(f"lambda包装连接失败: {conn_error}")
                    header.sortIndicatorChanged.connect(
                        self._on_sort_indicator_changed, 
                        Qt.QueuedConnection
                    )
                    self.logger.debug("✅ 排序信号连接成功 (QueuedConnection)")
                except Exception as queue_error:
                    try:
                        # 方法3: 降级到DirectConnection
                        self.logger.warning(f"QueuedConnection连接失败: {queue_error}")
                        header.sortIndicatorChanged.connect(
                            self._on_sort_indicator_changed,
                            Qt.DirectConnection
                        )
                        self.logger.debug("✅ 排序信号连接成功 (DirectConnection)")
                    except Exception as direct_error:
                        # 方法4: 最简单的直接连接
                        self.logger.warning(f"DirectConnection也失败: {direct_error}")
                        header.sortIndicatorChanged.connect(self._on_sort_indicator_changed)
                        self.logger.debug("✅ 排序信号连接成功 (默认连接)")
            
            # 设置表头可点击
            header.setSectionsClickable(True)
            
            self.logger.debug("排序功能设置完成")
            
        except Exception as e:
            self.logger.error(f"设置排序功能失败: {e}")
            # 即使设置失败，也不要抛出异常，让应用程序继续运行
    
    def _on_sort_indicator_changed(self, logical_index: int, order, *args):
        """
        响应排序指示器变化
        
        Args:
            logical_index: 列索引
            order: 排序顺序 (Qt.SortOrder枚举值)
            *args: 额外参数（兼容Qt不同版本的信号参数）
        """
        try:
            # 🔧 [排序修复] 导入Qt来确保枚举值正确处理
            from PyQt5.QtCore import Qt
            
            # 🔧 [修复标识] 记录额外参数信息，用于调试Qt信号参数问题
            if args:
                self.logger.debug(f"🔧 [Qt信号调试] 排序信号额外参数: {args}")
            
            # 检查列索引是否有效
            if logical_index < 0:
                self.logger.debug(f"无效的列索引: {logical_index}，忽略排序请求")
                return
            
            # 🔧 [排序修复] 安全地转换排序顺序枚举
            try:
                if order == Qt.AscendingOrder:
                    sort_order = SortOrder.ASCENDING
                elif order == Qt.DescendingOrder:
                    sort_order = SortOrder.DESCENDING
                else:
                    sort_order = SortOrder.NONE
            except Exception as enum_error:
                self.logger.warning(f"排序顺序转换失败: {enum_error}, 使用默认升序")
                sort_order = SortOrder.ASCENDING
            
            # 获取列名
            column_name = ""
            if logical_index < self.table_widget.columnCount():
                header_item = self.table_widget.horizontalHeaderItem(logical_index)
                if header_item:
                    column_name = header_item.text()
            
            # 🔧 [字段映射修复] 将UI显示名称转换为数据库字段名
            db_column_name = self._convert_ui_name_to_db_field(column_name)
            
            # 添加或更新排序列
            self.add_sort_column(logical_index, sort_order, db_column_name)
            
            self.logger.info(f"排序指示器变化: 列{logical_index}({db_column_name}) -> {sort_order.value}")
            
        except Exception as e:
            self.logger.error(f"处理排序指示器变化失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
    
    def add_sort_column(self, column_index: int, order: SortOrder, column_name: str = ""):
        """
        添加排序列
        
        Args:
            column_index: 列索引
            order: 排序顺序
            column_name: 列名
        """
        try:
            # 验证列索引有效性
            if column_index < 0:
                self.logger.warning(f"无效的列索引: {column_index}，忽略排序请求")
                return
            
            # 现在不再需要检查序号列，因为序号已移至垂直表头
            # （保留此注释作为历史记录）
            
            # 验证表格是否有效
            if not self.table_widget or column_index >= self.table_widget.columnCount():
                self.logger.warning(f"列索引超出范围: {column_index}，表格列数: {self.table_widget.columnCount() if self.table_widget else 0}")
                return
            
            # 移除已存在的同列排序
            self.sort_columns = [col for col in self.sort_columns if col.column_index != column_index]
            
            # 如果不是取消排序，添加新的排序列
            if order != SortOrder.NONE:
                new_priority = len(self.sort_columns)
                new_sort_column = SortColumn(
                    column_index=column_index,
                    order=order,
                    priority=new_priority,
                    column_name=column_name
                )
                self.sort_columns.append(new_sort_column)
                
                # 限制最大排序列数
                if len(self.sort_columns) > self.max_sort_columns:
                    # 移除优先级最低的列
                    self.sort_columns = sorted(self.sort_columns, key=lambda x: x.priority)
                    self.sort_columns = self.sort_columns[:self.max_sort_columns]
                    
                    # 重新分配优先级
                    for i, col in enumerate(self.sort_columns):
                        col.priority = i
            
            # 记录排序历史
            self._add_to_history()
            
            # 发出排序变化信号
            self.sort_changed.emit(self.sort_columns.copy())
            
            # 应用排序
            self.apply_sort()
            
            self.logger.info(f"添加排序列: {column_index}({column_name}) -> {order.value}, 当前排序列数: {len(self.sort_columns)}")
            
        except Exception as e:
            self.logger.error(f"添加排序列失败: {e}")
            # 发生异常时清除排序状态，避免状态不一致
            self.sort_columns.clear()
            self.sort_changed.emit([])
    
    def remove_sort_column(self, column_index: int):
        """
        移除排序列
        
        Args:
            column_index: 列索引
        """
        try:
            original_count = len(self.sort_columns)
            self.sort_columns = [col for col in self.sort_columns if col.column_index != column_index]
            
            # 重新分配优先级
            for i, col in enumerate(self.sort_columns):
                col.priority = i
            
            if len(self.sort_columns) != original_count:
                # 记录排序历史
                self._add_to_history()
                
                # 发出排序变化信号
                self.sort_changed.emit(self.sort_columns.copy())
                
                # 应用排序
                self.apply_sort()
                
                self.logger.info(f"移除排序列: {column_index}, 剩余排序列数: {len(self.sort_columns)}")
            
        except Exception as e:
            self.logger.error(f"移除排序列失败: {e}")
    
    def clear_all_sorts(self):
        """清除所有排序"""
        try:
            if self.sort_columns:
                self.sort_columns.clear()
                
                # 重置表头排序指示器
                header = self.table_widget.horizontalHeader()
                header.setSortIndicator(-1, Qt.AscendingOrder)
                
                # 记录排序历史
                self._add_to_history()
                
                # 发出排序变化信号
                self.sort_changed.emit([])
                
                # 恢复原始数据
                self.restore_original_order()
                
                self.logger.info("已清除所有排序")
            
        except Exception as e:
            self.logger.error(f"清除所有排序失败: {e}")
    
    def clear_sort(self):
        """清空排序状态 - 兼容性方法"""
        self.clear_all_sorts()
    
    def reset_sort_indicators(self):
        """重置排序指示器"""
        try:
            header = self.table_widget.horizontalHeader()
            header.setSortIndicator(-1, Qt.AscendingOrder)
            self.logger.info("已重置排序指示器")
        except Exception as e:
            self.logger.error(f"重置排序指示器失败: {e}")
            
        # 清空排序状态
        self.clear_all_sorts()
    
    def apply_sort(self):
        """应用当前排序设置"""
        try:
            if not self.sort_columns or self.original_data is None:
                return
            
            # 检查是否有无效的排序列（无效索引）
            valid_sort_columns = []
            for sort_col in self.sort_columns:
                if sort_col.column_index < 0:  # 无效索引
                    self.logger.warning(f"跳过无效的排序列: {sort_col.column_index}")
                    continue
                valid_sort_columns.append(sort_col)
            
            # 更新排序列列表，移除无效的列
            if len(valid_sort_columns) != len(self.sort_columns):
                self.sort_columns = valid_sort_columns
                self.logger.info(f"移除无效排序列，剩余: {len(self.sort_columns)} 列")
            
            if not self.sort_columns:
                self.logger.debug("没有有效的排序列，跳过排序")
                return
            
            # 获取原始数据的副本
            df = self.original_data.copy()
            
            if df.empty:
                return
            
            # 构建排序参数
            sort_columns = []
            sort_orders = []
            
            # 按优先级排序
            sorted_sort_columns = sorted(self.sort_columns, key=lambda x: x.priority)
            
            for sort_col in sorted_sort_columns:
                # 现在不需要调整列索引，因为序号不再占用数据列
                actual_col_index = sort_col.column_index
                
                if 0 <= actual_col_index < len(df.columns):
                    column_name = df.columns[actual_col_index]
                    sort_columns.append(column_name)
                    sort_orders.append(sort_col.order == SortOrder.ASCENDING)
                else:
                    self.logger.warning(f"列索引超出范围: {actual_col_index}, 数据列数: {len(df.columns)}")
            
            if sort_columns:
                # 执行排序
                sorted_df = df.sort_values(
                    by=sort_columns,
                    ascending=sort_orders,
                    na_position='last',  # NaN值放在最后
                    ignore_index=True
                )
                
                self.current_sorted_data = sorted_df
                
                # 更新表格显示
                self._update_table_display(sorted_df)
                
                # 发出排序应用完成信号
                self.sort_applied.emit(sorted_df)
                
                self.logger.info(f"排序应用完成: {len(sort_columns)} 列, 数据行数: {len(sorted_df)}")
            else:
                self.logger.warning("没有有效的排序列，跳过排序")
            
        except Exception as e:
            self.logger.error(f"应用排序失败: {e}")
            # 清除异常的排序状态
            self.sort_columns.clear()
            self.sort_changed.emit([])
    
    def _update_table_display(self, sorted_df: pd.DataFrame):
        """
        更新表格显示
        
        Args:
            sorted_df: 排序后的数据框
        """
        try:
            # 这里可以调用表格的刷新方法
            # 具体实现取决于表格组件的接口
            
            # 如果表格有update_row_numbers方法，调用它来更新序号
            if hasattr(self.table_widget, 'update_row_numbers'):
                self.table_widget.update_row_numbers()
            
            self.logger.debug("表格显示更新完成")
            
        except Exception as e:
            self.logger.error(f"更新表格显示失败: {e}")
    
    def restore_original_order(self):
        """恢复原始顺序"""
        try:
            if self.original_data is not None:
                self.current_sorted_data = self.original_data.copy()
                self._update_table_display(self.current_sorted_data)
                self.sort_applied.emit(self.current_sorted_data)
                self.logger.info("已恢复原始数据顺序")
            
        except Exception as e:
            self.logger.error(f"恢复原始顺序失败: {e}")
    
    def set_data(self, data: pd.DataFrame):
        """
        设置排序数据
        
        Args:
            data: 原始数据框
        """
        try:
            self.original_data = data.copy() if data is not None else None
            self.current_sorted_data = data.copy() if data is not None else None
            
            # 清除现有排序
            self.sort_columns.clear()
            
            self.logger.info(f"设置排序数据: {len(data) if data is not None else 0} 行")
            
        except Exception as e:
            self.logger.error(f"设置排序数据失败: {e}")
    
    def _convert_ui_name_to_db_field(self, ui_name: str) -> str:
        """
        将UI显示的列名转换为数据库字段名
        
        Args:
            ui_name: UI显示的列名
            
        Returns:
            数据库字段名
        """
        # 🔧 [字段映射修复] 统一的字段映射表
        field_mapping = {
            "2025年薪级工资": "grade_salary_2025",
            "2025年岗位工资": "position_salary_2025",
            "工号": "employee_id",
            "姓名": "employee_name",
            "部门名称": "department",
            "人员类别": "employee_type",
            "人员类别代码": "employee_type_code",
            "津贴": "allowance",
            "结余津贴": "balance_allowance",
            "2025年基础性绩效": "basic_performance_2025",
            "卫生费": "health_fee",
            "交通补贴": "transport_allowance",
            "物业补贴": "property_allowance",
            "通讯补贴": "communication_allowance",
            "2025年奖励性绩效预发": "performance_bonus_2025",
            "2025公积金": "provident_fund_2025",
            "住房补贴": "housing_allowance",
            "车补": "car_allowance",
            "补发": "supplement",
            "借支": "advance",
            "应发工资": "total_salary",
            "代扣代存养老保险": "pension_insurance"
        }
        
        # 返回映射的字段名，如果没有映射则返回原始名称
        return field_mapping.get(ui_name, ui_name)

    def _add_to_history(self):
        """添加到排序历史"""
        try:
            current_state = [
                {
                    'column_index': col.column_index,
                    'order': col.order.value,
                    'priority': col.priority,
                    'column_name': col.column_name
                }
                for col in self.sort_columns
            ]
            
            self.sort_history.append(current_state)
            
            # 限制历史记录大小
            if len(self.sort_history) > self.max_history_size:
                self.sort_history = self.sort_history[-self.max_history_size:]
            
        except Exception as e:
            self.logger.error(f"添加排序历史失败: {e}")
    
    def get_sort_info(self) -> Dict[str, Any]:
        """
        获取当前排序信息
        
        Returns:
            Dict: 排序信息
        """
        try:
            return {
                'sort_columns': [
                    {
                        'column_index': col.column_index,
                        'order': col.order.value,
                        'priority': col.priority,
                        'column_name': col.column_name
                    }
                    for col in self.sort_columns
                ],
                'total_columns': len(self.sort_columns),
                'max_columns': self.max_sort_columns,
                'has_row_number_column': self.has_row_number_column,
                'data_rows': len(self.original_data) if self.original_data is not None else 0
            }
            
        except Exception as e:
            self.logger.error(f"获取排序信息失败: {e}")
            return {}
    
    def get_sort_description(self) -> str:
        """
        获取排序描述文本
        
        Returns:
            str: 排序描述
        """
        try:
            if not self.sort_columns:
                return "无排序"
            
            descriptions = []
            sorted_columns = sorted(self.sort_columns, key=lambda x: x.priority)
            
            for i, col in enumerate(sorted_columns):
                order_text = "升序" if col.order == SortOrder.ASCENDING else "降序"
                col_name = col.column_name or f"列{col.column_index}"
                priority_text = f"({i+1})" if len(sorted_columns) > 1 else ""
                descriptions.append(f"{col_name}{priority_text}: {order_text}")
            
            return "; ".join(descriptions)
            
        except Exception as e:
            self.logger.error(f"获取排序描述失败: {e}")
            return "排序描述获取失败"


# 模块导出
__all__ = [
    "MultiColumnSortManager",
    "SortOrder", 
    "SortColumn"
]


if __name__ == "__main__":
    # 测试代码
    print("测试多列排序管理器...")
    
    # 这里可以添加单元测试
    manager = MultiColumnSortManager(None)
    print(f"排序管理器创建成功，最大排序列数: {manager.max_sort_columns}")
    
    print("多列排序管理器测试完成!")